<?php

declare(strict_types=1);

namespace MailScanModule\Commands;

use Doctrine\ORM\Query\QueryException;
use Entities\Service;
use Exceptions\Technical\NodeException;
use Libs\Exceptions\EntityNotFound;
use MailScanModule\Emailers\MailboxEmailer;
use Models\Products\BasketProduct;
use Models\Products\Package;
use Models\Products\Product;
use Psr\Log\LoggerInterface;
use Services\NodeService;
use Services\ServiceService;
use ServicesAdminModule\Facades\AddServiceFacade;
use ServicesAdminModule\Forms\AddServiceForm;
use ServicesAdminModule\Forms\AddServiceFormData;

class AddMailboxTierToRenewalsCommand
{
    public const METHOD_TYPE_RENEWAL = 'renewal';
    public const METHOD_TYPE_CUSTOM = 'custom';
    private const ONE_MONTH = 1;

    private const ALLOWED_PACKAGES = [
        Product::PRODUCT_REGISTERED_OFFICE,
        Product::PRODUCT_RENEWAL_REGISTERED_OFFICE,
        Package::PACKAGE_FULL_PRIVACY,
        Package::PACKAGE_FULL_PRIVACY_MONTHLY_REGULAR_PRICE,
        Package::PACKAGE_PRIVACY,
        Package::PACKAGE_RENEWAL_PRIVACY,
        Package::PACKAGE_CONTRACTOR,
        Package::PACKAGE_CONTRACTOR_RENEWAL,
        Package::PACKAGE_RENEWAL_COMPREHENSIVE_ULTIMATE,
        Package::PACKAGE_COMPREHENSIVE,
        Package::PACKAGE_RENEWAL_COMPREHENSIVE,
        Package::PACKAGE_ULTIMATE,
        Package::PACKAGE_RENEWAL_ULTIMATE,
        Package::PACKAGE_INTERNATIONAL,
        Package::PACKAGE_RENEWAL_INTERNATIONAL,
    ];

    private const ALLOWED_MAILBOX_PRODUCTS = [
        Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH,
        Product::PRODUCT_MAILBOX_STANDARD_INITIAL_3_MONTHS,
        // Product::PRODUCT_MAILBOX_PREMIUM_INITIAL_12_MONTHS, (missing features)
    ];

    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly NodeService $nodeService,
        private readonly ServiceService $serviceService,
        private readonly AddServiceFacade $addServiceFacade,
        private readonly MailboxEmailer $mailboxEmailer,
    ) {
    }

    public function execute(
        string $packageList,
        string $mailboxProductGiven = Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH,
        int $durationInMonths = self::ONE_MONTH,
        bool $sendEmail = true,
        bool $enableRetailers = true,
        bool $enableWholesalers = false,
        string $methodType = self::METHOD_TYPE_RENEWAL,
        ?string $startDate = null,
        ?string $endDate = null,
        ?int $companyId = null,
        bool $dryRun = true,
    ): void {
        try {
            [$startDate, $endDate] = $this->getDates($startDate, $endDate, $methodType);
            $packages = $this->getPackages($packageList);
            $mailboxProduct = $this->getMailboxProduct($mailboxProductGiven);

            $this->logStart(
                $packages,
                $mailboxProduct,
                $durationInMonths,
                $sendEmail,
                $enableRetailers,
                $enableWholesalers,
                $methodType,
                $dryRun,
                $startDate ? $startDate->format('Y-m-d') : 'null',
                $endDate ? $endDate->format('Y-m-d') : 'null',
                $companyId ? (string) $companyId : 'null'
            );

            $this->addMailboxToServices(
                $this->getEligibleServices(
                    $packages,
                    $enableRetailers,
                    $enableWholesalers,
                    $startDate,
                    $endDate,
                    $companyId
                ),
                $mailboxProduct,
                $sendEmail,
                $durationInMonths,
                $dryRun
            );
        } catch (\Throwable $e) {
            $this->logger->error(
                \sprintf(
                    'Command failed with error: %s',
                    $e->getMessage()
                ), ['e' => $e]
            );
        }
    }

    private function logStart(
        array $packages,
        BasketProduct $mailboxProduct,
        int $durationInMonths,
        bool $sendEmail,
        bool $enableRetailers,
        bool $enableWholesalers,
        string $methodType,
        bool $dryRun,
        ?string $initialDate,
        ?string $endDate,
        ?string $companyId,
    ): void {
        $this->logger->info(
            \sprintf(
                "\n\nRunning command with properties:\n"
                . implode(",\n", [
                    'Dry-Run: %s',
                    'Package List: %s',
                    'Mailbox Product Given: %s',
                    'Duration In Months: %s',
                    'Send Email: %s',
                    'Enable Retailers: %s',
                    'Enable Wholesalers: %s',
                    'Method Type: %s',
                    'Initial Date: %s',
                    "End Date: %s",
                    "Company ID: %s\n\n",
                ]),
                $dryRun ? 'true' : 'false',
                implode(', ', array_map(
                    fn (BasketProduct $package) => \sprintf("\n - %s (%s)", $package->getId(), $package->getName()),
                    $packages
                )),
                \sprintf('%s (%s)', $mailboxProduct->getId(), $mailboxProduct->getName()),
                $durationInMonths,
                $sendEmail ? 'true' : 'false',
                $enableRetailers ? 'true' : 'false',
                $enableWholesalers ? 'true' : 'false',
                $methodType,
                $initialDate ?? 'null',
                $endDate ?? 'null',
                $companyId ?? 'null'
            )
        );
    }

    /**
     * @throws EntityNotFound
     * @throws NodeException
     */
    private function addMailboxToServices(
        iterable $services,
        BasketProduct $mailboxProduct,
        bool $sendEmail,
        int $durationInMonths,
        bool $dryRun,
    ): void {
        if (empty($services)) {
            $this->logger->info('No services found');

            return;
        }

        $this->logger->info(\sprintf(
            'Adding Product %s (%s) to services',
            $mailboxProduct->getId(),
            $mailboxProduct->getName()
        ));

        /** @var Service $service */
        foreach ($services as $service) {
            try {
                if (!$dryRun) {
                    $this->addMailbox($service, $mailboxProduct, $durationInMonths);
                }

                $this->logger->info(\sprintf('Mailbox added to service %s', $service->getId()));

                if (!$dryRun) {
                    $this->sendEmail($service, $durationInMonths, $mailboxProduct, $sendEmail);
                }

                $this->logger->info(\sprintf(
                    'Email sent for service %s, to customer %s (%s)',
                    $service->getId(),
                    $service->getCustomer()->getId(),
                    $service->getCustomer()->getEmail()
                ));
            } catch (\RuntimeException $e) {
                $this->logger->info($e->getMessage());
            }
        }
    }

    /**
     * @throws NodeException
     * @throws EntityNotFound
     */
    private function addMailbox(
        Service $service,
        BasketProduct $mailboxProduct,
        int $durationInMonths,
    ): void {
        $existingService = $service->getCompany()->getActiveOrLatestMailboxService();

        if (!empty($existingService) && !$existingService->isRegisteredOfficeService()) {
            throw new \RuntimeException(\sprintf('Service %s already has a mailbox service', $service->getId()));
        }

        $this->addServiceFacade->addService(
            $this->createAddServiceFormData($mailboxProduct, $durationInMonths),
            $service->getCompany()
        );
    }

    /**
     * @throws NodeException
     */
    private function sendEmail(
        Service $service,
        int $durationInMonths,
        BasketProduct $mailboxProduct,
        bool $sendEmail,
    ): void {
        if (!$sendEmail) {
            return;
        }

        $this->mailboxEmailer->sendFreeTrialEmail(
            $service->getCustomer(),
            [
                'firstName' => $service->getCustomer()->getFirstName(),
                'email' => $service->getCustomer()->getEmail(),
                'durationInMonths' => $durationInMonths,
                'startDate' => $this->getFreeTrialStartDate()->format('d F Y'),
                'productName' => Service::$types[$mailboxProduct->getServiceTypeId()],
                'productPrice' => $mailboxProduct->getRenewalProduct()->getPrice($service->getCustomer()),
                'currentRegisteredOfficeProductName' => $service->getServiceName(),
                'features' => $this->getFeatures($mailboxProduct),
                'payAsYouGoPrice' => $this->getPayAsYouGoProductPrice(),
                'companyName' => $service->getCompany()->getCompanyName(),
            ]
        );
    }

    /**
     * @throws NodeException
     */
    private function getPackages(string $packageList): array
    {
        $packages = [];

        foreach (explode(',', $packageList) as $package) {
            $basketProduct = $this->nodeService->requiredProductByName($package);

            if (!in_array($basketProduct->getNodeName(), self::ALLOWED_PACKAGES)) {
                throw new \InvalidArgumentException(\sprintf('One of the included packages is not supported by this command: %s (%s)', $basketProduct->getId(), $basketProduct->getName()));
            }

            $packages[] = $basketProduct;
        }

        return $packages;
    }

    /**
     * @throws NodeException
     */
    private function getMailboxProduct(string $mailboxProductGiven): BasketProduct
    {
        $basketProduct = $this->nodeService->requiredProductByName($mailboxProductGiven);

        if (!in_array($basketProduct->getNodeName(), self::ALLOWED_MAILBOX_PRODUCTS)) {
            throw new \InvalidArgumentException('The mailbox product given is not supported by this command.');
        }

        return $basketProduct;
    }

    /**
     * @throws QueryException
     */
    private function getEligibleServices(
        array $packages,
        bool $enableRetailers,
        bool $enableWholesalers,
        ?\DateTime $initialDate,
        ?\DateTime $endDate,
        ?int $companyId,
    ): iterable {
        $services = $this->serviceService->getMailboxFreeTrialEligibleServices(
            array_map(fn (BasketProduct $product) => $product->getId(), $packages),
            $enableRetailers,
            $enableWholesalers,
            $initialDate,
            $endDate
        );

        if (!empty($companyId)) {
            return array_filter(
                is_array($services) ? $services : iterator_to_array($services),
                fn (Service $service) => $service->getCompany()->getId() === $companyId
            );
        }

        return $services;
    }

    private function getDates(?string $initialDate, ?string $endDate, string $methodType): array
    {
        if ($methodType === self::METHOD_TYPE_RENEWAL) {
            return [null, null];
        }

        if ($methodType === self::METHOD_TYPE_CUSTOM && (empty($initialDate) || empty($endDate))) {
            throw new \InvalidArgumentException('Initial and end date must be provided for custom method');
        }

        try {
            return [new \DateTime($initialDate), new \DateTime($endDate)];
        } catch (\Exception $e) {
            throw new \InvalidArgumentException(\sprintf('Invalid date format: %s', $e->getMessage()));
        }
    }

    private function createAddServiceFormData(
        BasketProduct $mailboxProduct,
        int $durationInMonths,
    ): AddServiceFormData {
        return (new AddServiceFormData())
            ->setProductName($mailboxProduct->getNodeName())
            ->setDuration(\sprintf('+%d month%s', $durationInMonths, $durationInMonths === 1 ? '' : 's'))
            ->setStartDate($this->getFreeTrialStartDate()->format('Y-m-d'))
            ->setPaymentDate( new \DateTime())
            ->setReason(AddServiceForm::REASON_COMMAND)
            ->setReference('AddMailboxTierToRenewalsCommand')
            ->setTotalAmount(0);
    }

    private function getFeatures(BasketProduct $mailboxProduct): array
    {
        return match ($mailboxProduct->getServiceTypeId()) {
            Service::TYPE_MAILBOX_STANDARD => [
                '<strong>Secure Mail Handling:</strong> We receive, scan, and store all your government and business mail securely in your digital mailbox.',
                '<strong>Free Scans of Business Mail:</strong> View 10 free business mail scans per month.',
                '<strong>Free Notifications of New mail:</strong> Receive notifications when new mail arrives.',
                '<strong>Parcel Reception and Notification:</strong> Get notified when your parcels arrive and collect them from our London office.',
                '<strong>Optional Mail Forwarding:</strong> Have your mail physically forwarded to your address for an additional fee, with our fast and reliable service.',
            ],
            default => throw new \RuntimeException(\sprintf('Service type %s features not implemented for this command.', $mailboxProduct->getServiceTypeId())),
        };
    }

    /**
     * @throws NodeException
     */
    private function getPayAsYouGoProductPrice(): float
    {
        return $this->nodeService->requiredProductByName((string) Product::PRODUCT_REGISTERED_OFFICE)->getMailboxPayToReleaseFeePostItem();
    }

    private function getFreeTrialStartDate(): \Datetime
    {
        return (new \DateTime())->modify('+1 day');
    }
}
