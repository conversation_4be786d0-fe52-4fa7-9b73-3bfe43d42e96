<?php

declare(strict_types=1);

namespace MailScanModule\Emailers;

use chfiling\type\DocumentContent;
use EmailModule\IEmail;
use EmailModule\IEmailGateway;
use EmailModule\IEmailLog;
use EmailModule\Loaders\IEmailLoader;
use Entities\Company;
use Entities\Customer;
use FrontModule\controlers\ManagePaymentControler;
use FrontModule\controlers\MyServicesControler;
use MailScanModule\ApiClient\IMailroomApiClient;
use MailScanModule\Deciders\MailboxEmailDecider;
use MailScanModule\Dto\MailboxEmailData;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Enums\InboxStatusEnum;
use MailScanModule\Enums\PostItemSenderEnum;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Factories\MailboxEmailFactory;
use MailScanModule\Factories\MailroomPostItemDataFactory;
use MailScanModule\Providers\MailboxEmailNameProvider;
use Psr\Log\LoggerInterface;
use RouterModule\Generators\IUrlGenerator;
use UserModule\Creators\IAuthTokenCreator;
use Utils\File;

class MailboxEmailer
{
    public const PROCESSING_STEP_NO_ID_CHECK = 'no-id-check';
    public const PROCESSING_STEP_NO_MAILBOX_SERVICE = 'no-mailbox-service';
    public const PROCESSING_STEP_SERVICE_OVERDUE = 'service-overdue';
    public const PROCESSING_STEP_RELEASED = 'released';
    public const PROCESSING_STEP_FAILED_TO_CHARGE = 'failed-to-charge';
    public const PROCESSING_STEP_RTS = 'return-to-sender';
    public const PROCESSING_STEP_WAITING_FOR_PAYMENT = 'waiting-for-payment';
    public const PROCESSING_STEP_UPDATE = 'updating-status';
    public const PROCESSING_STEP_MISSING_FORWARDING_ADDRESS = 'missing-mail-forwarding-address';
    public const MAILBOX_FREE_TRIAL_EMAIL = 'mailbox-free-trial-email';
    public const MAILBOX_FREE_TRIAL_EMAIL_SUBJECT = 'Get ready for your 1-month free trial of Mailbox!';
    private const MAILBOX_FOLDER_NAME = 'Mailbox-Emails';
    private const MAX_LENGTH_IN_BYTES = 104857600;

    public function __construct(
        private IEmailGateway $emailGateway,
        private IEmailLoader $emailLoader,
        private IMailroomApiClient $mailroomApiClient,
        private MailboxEmailFactory $mailboxEmailFactory,
        private MailboxEmailNameProvider $mailboxEmailProvider,
        private MailboxEmailDecider $mailboxEmailDecider,
        private readonly LoggerInterface $logger,
        private readonly IUrlGenerator $urlGenerator,
        private readonly IAuthTokenCreator $authTokenCreator,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function sendOneEmailForEachItem(Company $company, array $items, string $processingStep, bool $dryRun = true): array
    {
        $itemsForApi = [];
        foreach ($items as $item) {
            $emailData = $this->mailboxEmailFactory->createFromMailroomPostItemData($company, $item);
            $emailName = $this->mailboxEmailProvider->getMailboxEmailName($emailData, $processingStep);

            if (
                $processingStep === self::PROCESSING_STEP_UPDATE
                && $this->mailboxEmailDecider->reminderShouldBeSent($emailData, $emailName)
            ) {
                $emailName = $this->mailboxEmailProvider->getReminderEmailName($emailName);
                $this->logger->info(
                    sprintf(
                        'Sending reminder email %s to %s, regarding post item %s and company number %s',
                        $emailName,
                        $company->getCustomer()->getEmail(),
                        $item->getId(),
                        $company->getCompanyNumber()
                    )
                );
            }

            if (is_null($emailName)) {
                $this->logger->info(
                    sprintf(
                        'No email to send regarding post item %s and company number %s',
                        $item->getId(),
                        $company->getCompanyNumber()
                    )
                );
                continue;
            }

            if (
                $processingStep === self::PROCESSING_STEP_UPDATE
                && (!$this->mailboxEmailDecider->isAReminderEmail($emailName))
            ) {
                $this->logger->info(
                    sprintf(
                        'Skipping email %s to %s, regarding post item %s and company number %s (updating script should send only reminder emails)',
                        $emailName,
                        $company->getCustomer()->getEmail(),
                        $item->getId(),
                        $company->getCompanyNumber()
                    )
                );
                continue;
            }

            if ($emailData->lastEmailSent === $emailName) {
                $this->logger->info(
                    sprintf(
                        'Skipping email %s to %s, regarding post item %s and company number %s (email already sent)',
                        $emailName,
                        $company->getCustomer()->getEmail(),
                        $item->getId(),
                        $company->getCompanyNumber()
                    )
                );
                continue;
            }

            $this->logger->info(
                sprintf(
                    'Sending email %s to %s, regarding post item %s and company number %s',
                    $emailName,
                    $company->getCustomer()->getEmail(),
                    $item->getId(),
                    $company->getCompanyNumber()
                )
            );

            if ($dryRun) {
                $itemsForApi[] = [
                    'post_item_id' => $item->getId(),
                    'email_log_id' => null,
                    'email_name' => $emailName,
                ];
                continue;
            }

            try {
                $emailLog = $this->sendEmail($company, $emailName, $emailData, $item);

                $itemsForApi[] = [
                    'post_item_id' => $item->getId(),
                    'email_log_id' => $emailLog->getId(),
                    'email_name' => $emailName,
                ];
            } catch (\Throwable $e) {
                $this->logger->critical($e->getMessage(), ['exception' => $e]);
            }
        }

        if (!empty($itemsForApi) && !$dryRun) {
            $this->mailroomApiClient->setLastEmailSent($itemsForApi);
        }

        return $itemsForApi;
    }

    /**
     * @throws \Exception
     */
    public function sendCourtLetterEmailNotification(Company $company): IEmailLog
    {
        $mockItem = MailroomPostItemDataFactory::MockFromArray([
            MailroomPostItemDataFactory::KEY_COMPANY_NAME => $company->getCompanyNumber(),
            MailroomPostItemDataFactory::KEY_COMPANY_NUMBER => $company->getCompanyNumber(),
            MailroomPostItemDataFactory::KEY_TYPE => PostItemTypeEnum::TYPE_STATUTORY->value,
            MailroomPostItemDataFactory::KEY_SENDER => PostItemSenderEnum::SENDER_COURT_LETTER->value,
            MailroomPostItemDataFactory::KEY_STATUS => StatusEnum::STATUS_SCAN_ONLY->value,
        ]);

        $emailData = $this->mailboxEmailFactory->createFromMailroomPostItemData(
            $company,
            $mockItem
        );

        $emailData->withinQuota = true;

        return $this->sendEmail(
            $company,
            MailboxEmailNameProvider::MAIL_RELEASED_SCAN,
            $emailData,
            $mockItem
        );
    }

    public function sendFreeTrialEmail(Customer $customer, array $context): IEmailLog
    {
        $context = array_merge($context, [
            'myServicesLink' => $this->generateLink(
                (string) MyServicesControler::PAGE_SERVICES,
                ['otp' => $this->generateOtp($customer)],
                true
            ),
        ]);

        $email = $this->emailLoader->getEmailById(
            self::MAILBOX_FREE_TRIAL_EMAIL,
            File::fromExistingPath(
                sprintf(
                    '%s/cms/Digital-Mailroom/%s/%s.html',
                    EMAIL_DIR,
                    self::MAILBOX_FOLDER_NAME,
                    self::MAILBOX_FREE_TRIAL_EMAIL
                )
            ),
            $context,
            IEmailLoader::DISABLE_MARKDOWN_PARSING
        );

        $email->setSubject(
            \sprintf(
                '%s, get ready for your %d-month free trial of %s',
                $customer->getFirstName(),
                $context['durationInMonths'],
                $context['productName'],
            )
        );
        $email->setTo($customer->getEmail());

        return $this->emailGateway->send($email, $customer);
    }

    public function sendTrialEndingEmail(array $context, bool $dryRun = false): IEmailLog
    {
        $context['myServicesLink'] = $this->generateLink(
            (string) MyServicesControler::PAGE_SERVICES,
            ['otp' => $this->generateOtp($context['customer'])],
            true
        );

        $email = $this->emailLoader->getEmailById(
            MailboxEmailNameProvider::TRIAL_ENDING_EMAIL,
            File::fromExistingPath(
                sprintf(
                    '%s/cms/Digital-Mailroom/%s/%s.html',
                    EMAIL_DIR,
                    self::MAILBOX_FOLDER_NAME,
                    MailboxEmailNameProvider::TRIAL_ENDING_EMAIL
                )
            ),
            $context,
            IEmailLoader::DISABLE_MARKDOWN_PARSING
        );

        $email->setTag1('mailbox');
        $email->setTag2(MailboxEmailNameProvider::TRIAL_ENDING_EMAIL);
        $email->setFrom("<EMAIL>", 'Companies Made Simple');
        $email->setTo($context['customerEmail']);
        $email->setSubject(
            \sprintf(
                '%s, here’s what to expect next from your mailbox service',
                $context['firstName']
            )
        );

        return $this->emailGateway->send($email, $context['customer']);
    }

    private function loadEmail(string $templateName, Customer $customer, array $context): IEmail
    {
        $email = $this->emailLoader->getEmail(
            File::fromExistingPath(
                sprintf(
                    '%s/cms/Digital-Mailroom/%s/%s.md',
                    EMAIL_DIR,
                    self::MAILBOX_FOLDER_NAME,
                    $templateName
                )
            ),
            $context
        );

        $email->setTo($customer->getEmail());

        return $this->addAttachments($customer, $email, $context);
    }

    private function addAttachments(Customer $customer, IEmail $email, array $context): IEmail
    {
        $attachmentLink = $context['postItem']->getDetail('pdf_link') ?? null;

        if (
            $customer->hasMailboxAttachmentsEnabled()
            && !is_null($attachmentLink)
            && $context['emailData']->isReleased
        ) {
            $email->addAttachment(
                $this->getAttachmentName($context['postItem']),
                file_get_contents(
                    $attachmentLink,
                    false,
                    null,
                    0,
                    self::MAX_LENGTH_IN_BYTES
                ),
                DocumentContent::PDF
            );
        }

        return $email;
    }

    private function sendEmail(
        Company $company,
        string $emailName,
        MailboxEmailData $emailData,
        MailroomPostItemData $item,
    ): IEmailLog {
        $customer = $company->getCustomer();
        $email = $this->loadEmail(
            templateName: $emailName,
            customer: $customer,
            context: [
                'emailData' => $emailData,
                'postItem' => $item,
                'otpLinks' => $this->getLinks($customer, $company),
            ],
        );

        return $this->emailGateway->send($email, $customer);
    }

    private function getAttachmentName(MailroomPostItemData $postItem): string
    {
        try {
            $sanitizedCompanyName = preg_replace('/[\/\\\\?%*:|"<>]/', '-', $postItem->getCompanyName());
            $sanitizedDtc = preg_replace('/[\/\\\\?%*:|"<> :]/', '-', $postItem->getDtc()->format('Y-m-d-H-i-s'));

            return sprintf('%s_%s.pdf', $sanitizedCompanyName, $sanitizedDtc);
        } catch (\Throwable $e) {
            return sprintf('post-item_%s.pdf', time());
        }
    }

    private function generateOtp(Customer $customer): string
    {
        try {
            return $this->authTokenCreator->createToken($customer)->getTokenString();
        } catch (\Exception $e) {
            return '';
        }
    }

    private function getLinks(Customer $customer, Company $company): array
    {
        $otp = $this->generateOtp($customer);

        return [
            'wallet' => $this->generateLink((string) ManagePaymentControler::MANAGE_PAYMENT_PAGE, ['otp' => $otp], true),
            'myServices' => $this->generateLink((string) MyServicesControler::PAGE_SERVICES, ['otp' => $otp], true),
            'companyInbox' => $this->generateLink('mail_scan_module.company_inbox', ['otp' => $otp]),
            'inboxSettings' => $this->generateLink('mail_scan_module.inbox_settings', ['otp' => $otp, 'company' => $company->getId()]),
            'idCheck' => $this->generateLink('id_entity_checks', ['otp' => $otp, 'company' => $company->getId()]),
        ];
    }

    private function generateLink(string $route, array $parameters, bool $old = false): string
    {
        return $this->urlGenerator->url(
            $route,
            $parameters,
            ($old ? IUrlGenerator::OLD_LINK : null) | IUrlGenerator::SECURE
        );
    }
}
