<?php

declare(strict_types=1);

namespace ServicesAdminModule\Forms;

use ServiceActivatorModule\Validators\NotDowngradeForbidden;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class AddServiceForm extends AbstractType
{
    public const DURATION_1_MONTH = '+1 month';
    public const DURATION_3_MONTHS = '+3 months';
    public const DURATION_6_MONTHS = '+6 months';
    public const DURATION_9_MONTHS = '+9 months';
    public const DURATION_12_MONTHS = '+12 months';

    public const REASON_GOODWILL = 'goodwill';
    public const REASON_NON_STANDARD = 'nonStandard';
    public const REASON_COMMAND = 'command';

    /**
     * @param FormBuilderInterface $builder
     * @param array                $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        if (!empty($options['subscriber'])) {
            $builder->addEventSubscriber($options['subscriber']);
        }

        $choices = [];
        if (!empty($options['choicesProvider'])) {
            $choices = array_flip($options['choicesProvider']->getProducts($options['company']));
        }

        $builder
            ->add(
                'productName',
                ChoiceType::class,
                [
                    'label' => 'Service Offer',
                    'choices' => $choices,
                    'placeholder' => '--- Select product ---',
                    'constraints' => [
                        new NotDowngradeForbidden(['company' => $options['company']]),
                    ],
                ]
            )
            ->add(
                'reason',
                ChoiceType::class,
                [
                    'choices' => array_flip([
                        self::REASON_GOODWILL => 'Gesture of Goodwill',
                        self::REASON_NON_STANDARD => 'Non-Standard payment method was used',
                    ]),
                    'expanded' => true,
                ]
            )
            ->add(
                'paymentDate',
                DateType::class,
                [
                    'label' => 'Payment Date',
                    'widget' => 'single_text',
                    'format' => 'yyyy-MM-dd',
                    'invalid_message' => 'Please provide a valid date',
                    'required' => false,
                ]
            )
            ->add(
                'totalAmount',
                NumberType::class,
                [
                    'label' => 'Total Amount',
                    'required' => false,
                ]
            )
            ->add(
                'reference',
                TextType::class,
                [
                    'required' => false,
                ]
            )
            ->add(
                'previousProductId',
                HiddenType::class,
                [
                    'mapped' => false,
                ]
            )
            ->add(
                'previousStartDate',
                HiddenType::class,
                [
                    'mapped' => false,
                ]
            )
            ->add(
                'add',
                SubmitType::class,
                [
                    'label' => 'Add Service',
                ]
            );
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'validation_groups' => function (FormInterface $form) {
                    /** @var AddServiceFormData $data */
                    $data = $form->getData();

                    return ['Default', $data->getReason()];
                },
                'subscriber' => null,
                'choicesProvider' => null,
                'company' => null,
            ]
        );
    }

    /**
     * @return null
     */
    public function getName()
    {
        return null;
    }
}
